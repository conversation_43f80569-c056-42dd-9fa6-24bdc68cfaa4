import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import { useState, useCallback, useMemo, useRef } from 'react';

import api, { ResponseApi } from 'services/api';

import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import { ProdutoOptionProps } from 'pages/EntradaMercadoria/EntradaManual/LancamentoProdutos/ModalAdicionarProduto/validationForm';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import TipoProdutoEnum, { TipoProduto } from 'constants/enum/tipoProduto';

import { ModalVincularProduto } from '../ModalVincularProduto';

export enum EntradaMercadoriaStatusVinculoProduto {
  NAO_VINCULADO = 'NAO_VINCULADO',
  PENDENTE_INFORMAR_VARIACOES = 'PENDENTE_INFORMAR_VARIACOES',
  VINCULADO = 'VINCULADO',
}

export type InformacoesRodape = {
  totalProdutos: number;
  quantidadeItens: number;
  valorTotalProdutos: number;
  todosProdutosVinculados: boolean;
};

export type Produto = {
  isOpen: boolean;
  documentoFiscalItemId: string;
  descricaoProdutoNota: string;
  statusVinculo: EntradaMercadoriaStatusVinculoProduto;
  dadosAdicionais: string | null;
  quantidade: number;
  valorUnitario: number;
  valorTotal: number;
  cfopEntrada: string;
  cfopNota: string;
  ncm: string;
  codigoCest: string;
  produtoVinculado: {
    id: string;
    nome: null | string;
    conversao: number;
    novaQuantidade: number;
    codigoBarrasNota: string | null;
    codigoBarrasCadastro: string | null;
    dadosAdicionais: string | null;
    tipoProduto: number;
    volumeUnitario: boolean;
    referencia: string | null;
    precoCompra: number;
  } | null;
};

export type ProdutoPaginadoRetorno = {
  totalProdutos: number;
  totalItens: number;
  valorTotal: number;
  todosProdutosVinculados: boolean;
  registros: Produto[];
};

export function useProdutosVinculacao(entradaMercadoriaId: string | null) {
  const { casasDecimais } = usePadronizacaoContext();
  const queryClient = useQueryClient();

  const [openById, setOpenById] = useState<Record<string, boolean>>({});

  const toggleIsOpen = useCallback((id: string) => {
    setOpenById((prev) => ({ ...prev, [id]: !prev[id] }));
  }, []);

  const isOpen = (id: string) => !!openById[id];

  const [isLoading, setIsLoading] = useState(false);
  const lockRef = useRef(false);

  const buscarItensPaginacao = useCallback(
    async ({
      pageParam = 1,
      id,
    }: {
      pageParam?: number;
      id: string | null;
    }) => {
      if (!id) {
        return {
          registros: [],
          totalItens: 0,
          totalProdutos: 0,
          valorTotal: 0,
          todosProdutosVinculados: false,
          bloquearAlteracao: false,
        };
      }
      const response = await api.get<void, ResponseApi<ProdutoPaginadoRetorno>>(
        ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_LISTAR_ITENS_PAGINADOS_IMPORTACAO_XML,
        {
          params: {
            id: id,
            paginaAtual: pageParam,
            tamanhoPagina: 25,
            campoOrdenacao: 'NomeProduto',
            direcaoOrdenacao: 'ASC',
          },
        }
      );

      if (!response?.sucesso) {
        throw new Error('Erro ao carregar produtos');
      }

      return {
        ...response.dados,
        registros:
          response.dados.registros?.map((registro) => ({
            ...registro,
          })) || [],
      };
    },
    []
  );

  const {
    data,
    fetchNextPage,
    isLoading: isLoadingQuery,
    isFetchingNextPage,
    isFetching,
    hasNextPage,
  } = useInfiniteQuery<ProdutoPaginadoRetorno>({
    queryKey: ['produtos-vinculacao', entradaMercadoriaId],
    queryFn: ({ pageParam }) =>
      buscarItensPaginacao({
        pageParam: (pageParam as number) || 1,
        id: entradaMercadoriaId,
      }),
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      const totalPaginas = Math.ceil((lastPage.totalProdutos ?? 0) / 25);
      const paginaAtual = allPages.length;
      return paginaAtual < totalPaginas ? paginaAtual + 1 : undefined;
    },
    enabled: !!entradaMercadoriaId,
    refetchOnWindowFocus: false,
  });

  const itensListagem = useMemo(
    () => data?.pages?.flatMap((page) => page.registros) ?? [],
    [data]
  );

  const produtos = itensListagem;

  const informacoesRodape = useMemo(() => {
    return {
      quantidadeItens: data?.pages?.[0]?.totalItens ?? 0,
      totalProdutos: data?.pages?.[0]?.totalProdutos ?? 0,
      valorTotalProdutos: data?.pages?.[0]?.valorTotal ?? 0,
      todosProdutosVinculados:
        data?.pages?.[0]?.todosProdutosVinculados ?? false,
    };
  }, [data?.pages]);

  const todosProdutosVinculados = useMemo(() => {
    return data?.pages?.[0]?.todosProdutosVinculados ?? false;
  }, [data?.pages]);

  const encontrarProximoProdutoParaVincular = useCallback(
    (index: number, produtosAtualizados: Produto[]): number | null => {
      const produto = produtosAtualizados[index];

      if (
        produto &&
        produto.statusVinculo !==
          EntradaMercadoriaStatusVinculoProduto.VINCULADO
      ) {
        return index;
      } else if (index < produtosAtualizados.length - 1) {
        return encontrarProximoProdutoParaVincular(
          index + 1,
          produtosAtualizados
        );
      }
      return null;
    },
    []
  );

  const vincularProduto = useCallback(
    async (
      index: number,
      isEdicao = false,
      produtosAtualizados = itensListagem,
      produtoPendenteVariacoes?: ProdutoOptionProps
    ) => {
      const {
        documentoFiscalItemId,
        descricaoProdutoNota,
        quantidade,
        valorUnitario,
        valorTotal,
        cfopNota,
        cfopEntrada,
        codigoCest,
        ncm,
        produtoVinculado,
        dadosAdicionais,
        statusVinculo,
      } = produtosAtualizados[index];

      const obterCodigoBarras = () => {
        const produtoTipoVariacao =
          produtoVinculado?.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO;

        if (produtoTipoVariacao) {
          return '';
        }

        if (produtoVinculado?.codigoBarrasCadastro) {
          return produtoVinculado?.codigoBarrasCadastro;
        }

        if (produtoVinculado?.codigoBarrasNota) {
          return produtoVinculado?.codigoBarrasNota;
        }

        return '';
      };

      if (entradaMercadoriaId) {
        try {
          const {
            success,
            quantidade: novaQuantidade,
            valorUnitario: novoValorUnitario,
            cfop: novoCfop,
            codigoBarrasCadastro: novoCodigoBarrasCadastro,
            temProximoProdutoParaVincular,
            produtoVinculado: novoProdutoVinculado,
          } = await ModalVincularProduto({
            produtoPendenteVariacoes,
            casasDecimaisQuantidade: casasDecimais.casasDecimaisQuantidade,
            casasDecimaisValor: casasDecimais.casasDecimaisValor,
            produto: {
              documentoFiscalItemId,
              descricaoProduto: descricaoProdutoNota,
              quantidade,
              valorUnitario,
              valorTotal,
              cfop: cfopEntrada,
              cfopNota,
              codigoGTINEAN: obterCodigoBarras(),
              codigoBarrasNota: produtoVinculado?.codigoBarrasNota || null,
              ncm,
              codigoCest,
              dadosAdicionais: dadosAdicionais ?? '',
              tipoProduto: (produtoVinculado?.tipoProduto ??
                TipoProdutoEnum.PRODUTO_SIMPLES) as TipoProduto,
            },
            produtoJaVinculado:
              statusVinculo === EntradaMercadoriaStatusVinculoProduto.VINCULADO,
            totalProdutos: informacoesRodape.totalProdutos,
            entradaMercadoriaId,
            isEdicao,
            numeroItem:
              produtosAtualizados?.filter(
                ({ statusVinculo: statusVinculoProduto }) =>
                  statusVinculoProduto ===
                  EntradaMercadoriaStatusVinculoProduto.VINCULADO
              )?.length + 1,
            proximoItem: index + 1,
          });

          if (success) {
            queryClient.setQueryData(
              ['produtos-vinculacao', entradaMercadoriaId],
              (oldData: any) => {
                if (!oldData?.pages) return oldData;

                let itemEncontrado = false;
                let indexAtual = 0;

                const novasPages = oldData.pages.map((page: any) => {
                  if (itemEncontrado) return page;

                  const novosRegistros = page.registros.map((produto: any) => {
                    if (indexAtual === index && !itemEncontrado) {
                      itemEncontrado = true;
                      return {
                        ...produto,
                        valorUnitario: novoValorUnitario,
                        cfopEntrada: novoCfop,
                        statusVinculo:
                          EntradaMercadoriaStatusVinculoProduto.VINCULADO,
                        produtoVinculado: {
                          ...produto.produtoVinculado,
                          ...novoProdutoVinculado,
                          conversao:
                            produto.produtoVinculado?.conversao > 0
                              ? produto.produtoVinculado?.conversao
                              : 1,
                          novaQuantidade,
                          codigoBarrasCadastro: novoCodigoBarrasCadastro,
                        },
                      };
                    }
                    indexAtual++;
                    return produto;
                  });
                  return {
                    ...page,
                    registros: novosRegistros,
                  };
                });

                return {
                  ...oldData,
                  pages: novasPages,
                };
              }
            );
          }

          if (temProximoProdutoParaVincular) {
            const proximoIndex = encontrarProximoProdutoParaVincular(
              index + 1,
              produtosAtualizados
            );
            if (proximoIndex !== null) {
              await vincularProduto(proximoIndex, false, produtosAtualizados);
            }
          }
          return success;
        } catch (error) {
          return error;
        }
      }

      return false;
    },
    [
      itensListagem,
      entradaMercadoriaId,
      casasDecimais.casasDecimaisQuantidade,
      casasDecimais.casasDecimaisValor,
      informacoesRodape.totalProdutos,
      encontrarProximoProdutoParaVincular,
      queryClient,
    ]
  );

  const handleEditar = useCallback(
    async (index: number) => {
      await vincularProduto(index, true);
    },
    [vincularProduto]
  );

  const handleVincularProduto = useCallback(
    async (index: number, produtoPendenteVariacoes?: ProdutoOptionProps) => {
      await vincularProduto(index, false, produtos, produtoPendenteVariacoes);
    },
    [vincularProduto, produtos]
  );

  const buscarMaisItensNoScroll = useCallback(
    (containerRefElement?: HTMLDivElement | null) => {
      if (!containerRefElement) return;

      const { scrollHeight, scrollTop, clientHeight } = containerRefElement;
      const distanciaDoFinal = scrollHeight - scrollTop - clientHeight;
      const quaseNoFim = distanciaDoFinal < 500;
      if (
        quaseNoFim &&
        hasNextPage &&
        !isFetchingNextPage &&
        !lockRef.current
      ) {
        lockRef.current = true;
        fetchNextPage()
          .catch(() => {})
          .finally(() => {
            setTimeout(() => {
              lockRef.current = false;
            }, 150);
          });
      }
    },
    [hasNextPage, isFetchingNextPage, fetchNextPage]
  );

  const obterCorBackground = (
    statusVinculo: EntradaMercadoriaStatusVinculoProduto
  ): string => {
    const enumStatus = EntradaMercadoriaStatusVinculoProduto;
    const vinculado = statusVinculo === enumStatus.VINCULADO;
    const naoVinculado = statusVinculo === enumStatus.NAO_VINCULADO;

    if (vinculado) return 'teal.600';
    if (naoVinculado) return 'white';
    return 'aquamarine.100';
  };

  return {
    informacoesRodape,
    todosProdutosVinculados,
    isLoading: isLoadingQuery || isLoading || isFetchingNextPage || isFetching,
    toggleIsOpen,
    handleEditar,
    handleVincularProduto,
    itensListagem,
    buscarMaisItensNoScroll,
    obterCorBackground,
    isOpen,
  };
}
